{
  // 活动栏保持在左侧默认位置
  "workbench.activityBar.location": "default",

  // 主侧边栏在左侧
  "workbench.sideBar.location": "left",

  // 面板默认位置在右侧
  "workbench.panel.defaultLocation": "right",

  // 辅助侧边栏配置（用于放置 Augment）
  "workbench.auxiliaryBar.size": 450,

  // 界面显示设置
  "workbench.view.alwaysShowHeaderActions": true,
  "workbench.editor.showTabs": "multiple",
  "workbench.editor.tabSizing": "shrink",
  "workbench.panel.opensMaximized": "never",

  // 侧边栏尺寸设置
  "workbench.sideBar.size": 300,
  "workbench.tree.indent": 20,

  // 编辑器设置
  "editor.minimap.enabled": true,
  "editor.minimap.side": "right",

  // 主题设置
  "workbench.colorTheme": "Default Dark+",

  // Augment 相关优化设置
  "workbench.editor.wrapTabs": true,
  "workbench.editor.scrollToSwitchTabs": true,
  "workbench.startupEditor": "welcomePage",

  // GitHub Copilot / Codex 优化设置
  "github.copilot.enable": {
    "*": false
  },
  "github.copilot.inlineSuggest.enable": true,
  "github.copilot.advanced": {
    "listCount": 10,
    "inlineSuggestCount": 3
  },

  // 编辑器智能提示设置
  "editor.inlineSuggest.enabled": true,
  "editor.suggestSelection": "first",
  "editor.tabCompletion": "on",
  "editor.wordBasedSuggestions": "matchingDocuments",
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": true
  }
}
